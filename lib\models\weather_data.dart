class WeatherData {
  final String cityName;
  final double temperature;
  final double feelsLike;
  final int humidity;
  final double windSpeed;
  final int pressure;
  final String condition;
  final String description;
  final String icon;
  final double latitude;
  final double longitude;
  final DateTime dateTime;
  final int visibility;
  final double? tempMin;
  final double? tempMax;

  WeatherData({
    required this.cityName,
    required this.temperature,
    required this.feelsLike,
    required this.humidity,
    required this.windSpeed,
    required this.pressure,
    required this.condition,
    required this.description,
    required this.icon,
    required this.latitude,
    required this.longitude,
    required this.dateTime,
    required this.visibility,
    this.tempMin,
    this.tempMax,
  });

  factory WeatherData.fromJson(Map<String, dynamic> json) {
    return WeatherData(
      cityName: json['name'] ?? 'Näbelli şäher',
      temperature: (json['main']['temp'] as num).toDouble(),
      feelsLike: (json['main']['feels_like'] as num).toDouble(),
      humidity: json['main']['humidity'] as int,
      windSpeed: (json['wind']?['speed'] as num?)?.toDouble() ?? 0.0,
      pressure: json['main']['pressure'] as int,
      condition: json['weather'][0]['main'] as String,
      description: json['weather'][0]['description'] as String,
      icon: json['weather'][0]['icon'] as String,
      latitude: (json['coord']['lat'] as num).toDouble(),
      longitude: (json['coord']['lon'] as num).toDouble(),
      dateTime: DateTime.fromMillisecondsSinceEpoch(
        (json['dt'] as int) * 1000,
      ),
      visibility: json['visibility'] ?? 10000,
      tempMin: (json['main']['temp_min'] as num?)?.toDouble(),
      tempMax: (json['main']['temp_max'] as num?)?.toDouble(),
    );
  }

  factory WeatherData.fromForecastJson(
    Map<String, dynamic> json,
    Map<String, dynamic> cityData,
  ) {
    return WeatherData(
      cityName: cityData['name'] ?? 'Näbelli şäher',
      temperature: (json['main']['temp'] as num).toDouble(),
      feelsLike: (json['main']['feels_like'] as num).toDouble(),
      humidity: json['main']['humidity'] as int,
      windSpeed: (json['wind']?['speed'] as num?)?.toDouble() ?? 0.0,
      pressure: json['main']['pressure'] as int,
      condition: json['weather'][0]['main'] as String,
      description: json['weather'][0]['description'] as String,
      icon: json['weather'][0]['icon'] as String,
      latitude: (cityData['coord']['lat'] as num).toDouble(),
      longitude: (cityData['coord']['lon'] as num).toDouble(),
      dateTime: DateTime.fromMillisecondsSinceEpoch(
        (json['dt'] as int) * 1000,
      ),
      visibility: json['visibility'] ?? 10000,
      tempMin: (json['main']['temp_min'] as num?)?.toDouble(),
      tempMax: (json['main']['temp_max'] as num?)?.toDouble(),
    );
  }

  // Howa durumyny türkmençe terjime etmek
  String get turkmenDescription {
    switch (condition.toLowerCase()) {
      case 'clear':
        return 'Açyk howa';
      case 'clouds':
        return _getCloudDescription();
      case 'rain':
        return _getRainDescription();
      case 'snow':
        return 'Gar';
      case 'thunderstorm':
        return 'Ýyldyrym';
      case 'drizzle':
        return 'Çyg';
      case 'mist':
      case 'fog':
        return 'Duman';
      case 'smoke':
        return 'Tüsse';
      case 'haze':
        return 'Duman';
      case 'dust':
        return 'Tozan';
      case 'sand':
        return 'Çäge';
      case 'ash':
        return 'Kül';
      case 'squall':
        return 'Güýçli şemal';
      case 'tornado':
        return 'Tornado';
      default:
        return description;
    }
  }

  String _getCloudDescription() {
    if (description.contains('few')) return 'Az bulutly';
    if (description.contains('scattered')) return 'Dargadylan bulutlar';
    if (description.contains('broken')) return 'Köp bulutly';
    if (description.contains('overcast')) return 'Bulutly';
    return 'Bulutly';
  }

  String _getRainDescription() {
    if (description.contains('light')) return 'Ýeňil ýagyş';
    if (description.contains('moderate')) return 'Orta ýagyş';
    if (description.contains('heavy')) return 'Güýçli ýagyş';
    if (description.contains('extreme')) return 'Aşa güýçli ýagyş';
    return 'Ýagyş';
  }

  // Şemal ugruny türkmençe
  String getWindDirection() {
    // Bu funksiýa şemal ugry bar bolsa ulanylýar
    return 'Demirgazyk'; // Ýörite şemal ugry maglumat bar bolsa goşup bolar
  }

  // Görüş aralygy türkmençe
  String get visibilityText {
    if (visibility >= 10000) return 'Gowy görünýär';
    if (visibility >= 5000) return 'Orta görünýär';
    if (visibility >= 1000) return 'Pes görünýär';
    return 'Gaty pes görünýär';
  }

  // Çyglylyk derejesi türkmençe
  String get humidityText {
    if (humidity >= 80) return 'Gaty çygly';
    if (humidity >= 60) return 'Çygly';
    if (humidity >= 40) return 'Orta çygly';
    return 'Gurak';
  }

  // Şemal tizligi türkmençe
  String get windSpeedText {
    if (windSpeed >= 20) return 'Gaty güýçli şemal';
    if (windSpeed >= 15) return 'Güýçli şemal';
    if (windSpeed >= 10) return 'Orta şemal';
    if (windSpeed >= 5) return 'Ýeňil şemal';
    return 'Şemal ýok';
  }

  @override
  String toString() {
    return 'WeatherData(cityName: $cityName, temperature: $temperature°C, condition: $turkmenDescription)';
  }
}
