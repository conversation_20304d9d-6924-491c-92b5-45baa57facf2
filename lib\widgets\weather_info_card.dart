import 'package:flutter/material.dart';
import '../models/weather_data.dart';

class WeatherInfoCard extends StatelessWidget {
  final WeatherData weather;

  const WeatherInfoCard({
    super.key,
    required this.weather,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 8,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              const Color(0xFF00A651).withValues(alpha: 0.8),
              const Color(0xFF00A651),
            ],
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Şäher ady we wagty
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    weather.cityName,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                ),
                Text(
                  _formatTime(weather.dateTime),
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            
            // Esasy howa durumu
            Row(
              children: [
                // Temperatura we howa durumu
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            '${weather.temperature.round()}°',
                            style: const TextStyle(
                              fontSize: 48,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          ),
                          const Text(
                            'C',
                            style: TextStyle(
                              fontSize: 24,
                              fontWeight: FontWeight.normal,
                              color: Colors.white,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        weather.turkmenDescription,
                        style: const TextStyle(
                          fontSize: 16,
                          color: Colors.white,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Duýulýar: ${weather.feelsLike.round()}°C',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
                
                // Howa durumu ikony
                Expanded(
                  flex: 1,
                  child: Column(
                    children: [
                      Icon(
                        _getWeatherIcon(weather.condition),
                        size: 64,
                        color: Colors.white,
                      ),
                      if (weather.tempMin != null && weather.tempMax != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 8),
                          child: Text(
                            '${weather.tempMin!.round()}° / ${weather.tempMax!.round()}°',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Colors.white70,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            
            const SizedBox(height: 16),
            
            // Goşmaça maglumatlar
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.white.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildInfoItem(
                        Icons.water_drop,
                        'Çyglylyk',
                        '${weather.humidity}%',
                      ),
                      _buildInfoItem(
                        Icons.air,
                        'Şemal',
                        '${weather.windSpeed.toStringAsFixed(1)} m/s',
                      ),
                      _buildInfoItem(
                        Icons.compress,
                        'Basyş',
                        '${weather.pressure} hPa',
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildInfoItem(
                        Icons.visibility,
                        'Görünýär',
                        '${(weather.visibility / 1000).toStringAsFixed(1)} km',
                      ),
                      _buildInfoItem(
                        Icons.thermostat,
                        'Duýulýar',
                        '${weather.feelsLike.round()}°C',
                      ),
                      _buildInfoItem(
                        Icons.schedule,
                        'Täzelenen',
                        _formatTime(weather.dateTime),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoItem(IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(
          icon,
          size: 20,
          color: Colors.white,
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: const TextStyle(
            fontSize: 10,
            color: Colors.white70,
          ),
        ),
        Text(
          value,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
      ],
    );
  }

  IconData _getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
        return Icons.wb_sunny;
      case 'clouds':
        return Icons.cloud;
      case 'rain':
        return Icons.grain;
      case 'snow':
        return Icons.ac_unit;
      case 'thunderstorm':
        return Icons.flash_on;
      case 'drizzle':
        return Icons.grain;
      case 'mist':
      case 'fog':
        return Icons.cloud;
      case 'smoke':
        return Icons.cloud;
      case 'haze':
        return Icons.cloud;
      case 'dust':
      case 'sand':
        return Icons.cloud;
      case 'ash':
        return Icons.cloud;
      case 'squall':
        return Icons.air;
      case 'tornado':
        return Icons.tornado;
      default:
        return Icons.wb_sunny;
    }
  }

  String _formatTime(DateTime dateTime) {
    return '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
