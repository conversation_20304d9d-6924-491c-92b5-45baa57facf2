import 'dart:convert';
import 'package:http/http.dart' as http;
import '../models/weather_data.dart';

class WeatherService {
  // OpenWeatherMap API açary - bu ýerde öz API açaryňyzy goýuň
  static const String _apiKey = '4304d823d50e67662f862fba153220fd';
  static const String _baseUrl = 'https://api.openweathermap.org/data/2.5';

  Future<WeatherData?> getWeatherByCoordinates(double lat, double lon) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/weather?lat=$lat&lon=$lon&appid=$_apiKey&units=metric&lang=tk',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return WeatherData.fromJson(data);
      } else {
        print('API ýalňyşlygy: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Howa durumu maglumatlaryny almakda ýalňyşlyk: $e');
      return null;
    }
  }

  Future<WeatherData?> getWeatherByCity(String cityName) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/weather?q=$cityName,TM&appid=$_apiKey&units=metric&lang=tk',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        return WeatherData.fromJson(data);
      } else {
        print('API ýalňyşlygy: ${response.statusCode}');
        return null;
      }
    } catch (e) {
      print('Howa durumu maglumatlaryny almakda ýalňyşlyk: $e');
      return null;
    }
  }

  Future<List<WeatherData>> getForecast(double lat, double lon) async {
    try {
      final url = Uri.parse(
        '$_baseUrl/forecast?lat=$lat&lon=$lon&appid=$_apiKey&units=metric&lang=tk',
      );

      final response = await http.get(url);

      if (response.statusCode == 200) {
        final data = json.decode(response.body);
        final List<dynamic> forecastList = data['list'];
        
        return forecastList
            .map((item) => WeatherData.fromForecastJson(item, data['city']))
            .toList();
      } else {
        print('Çaklaýyş API ýalňyşlygy: ${response.statusCode}');
        return [];
      }
    } catch (e) {
      print('Çaklaýyş maglumatlaryny almakda ýalňyşlyk: $e');
      return [];
    }
  }

  // Howa durumyny türkmençe terjime etmek
  String translateWeatherCondition(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear sky':
        return 'Açyk howa';
      case 'few clouds':
        return 'Az bulutly';
      case 'scattered clouds':
        return 'Dargadylan bulutlar';
      case 'broken clouds':
        return 'Köp bulutly';
      case 'overcast clouds':
        return 'Bulutly';
      case 'light rain':
        return 'Ýeňil ýagyş';
      case 'moderate rain':
        return 'Orta ýagyş';
      case 'heavy intensity rain':
        return 'Güýçli ýagyş';
      case 'very heavy rain':
        return 'Gaty güýçli ýagyş';
      case 'extreme rain':
        return 'Aşa güýçli ýagyş';
      case 'freezing rain':
        return 'Buzly ýagyş';
      case 'light intensity shower rain':
        return 'Ýeňil ýagyş';
      case 'shower rain':
        return 'Ýagyş';
      case 'heavy intensity shower rain':
        return 'Güýçli ýagyş';
      case 'ragged shower rain':
        return 'Üýtgeýän ýagyş';
      case 'light snow':
        return 'Ýeňil gar';
      case 'snow':
        return 'Gar';
      case 'heavy snow':
        return 'Güýçli gar';
      case 'sleet':
        return 'Gar-ýagyş';
      case 'light shower sleet':
        return 'Ýeňil gar-ýagyş';
      case 'shower sleet':
        return 'Gar-ýagyş';
      case 'light rain and snow':
        return 'Ýeňil ýagyş we gar';
      case 'rain and snow':
        return 'Ýagyş we gar';
      case 'light shower snow':
        return 'Ýeňil gar';
      case 'shower snow':
        return 'Gar';
      case 'heavy shower snow':
        return 'Güýçli gar';
      case 'mist':
        return 'Duman';
      case 'smoke':
        return 'Tüsse';
      case 'haze':
        return 'Duman';
      case 'sand/dust whirls':
        return 'Çäge/tozan';
      case 'fog':
        return 'Duman';
      case 'sand':
        return 'Çäge';
      case 'dust':
        return 'Tozan';
      case 'volcanic ash':
        return 'Wulkan külü';
      case 'squalls':
        return 'Güýçli şemal';
      case 'tornado':
        return 'Tornado';
      case 'thunderstorm with light rain':
        return 'Ýeňil ýagyşly ýyldyrym';
      case 'thunderstorm with rain':
        return 'Ýagyşly ýyldyrym';
      case 'thunderstorm with heavy rain':
        return 'Güýçli ýagyşly ýyldyrym';
      case 'light thunderstorm':
        return 'Ýeňil ýyldyrym';
      case 'thunderstorm':
        return 'Ýyldyrym';
      case 'heavy thunderstorm':
        return 'Güýçli ýyldyrym';
      case 'ragged thunderstorm':
        return 'Üýtgeýän ýyldyrym';
      case 'thunderstorm with light drizzle':
        return 'Ýeňil çygly ýyldyrym';
      case 'thunderstorm with drizzle':
        return 'Çygly ýyldyrym';
      case 'thunderstorm with heavy drizzle':
        return 'Güýçli çygly ýyldyrym';
      default:
        return condition;
    }
  }
}
