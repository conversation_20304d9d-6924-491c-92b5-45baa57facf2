import 'package:flutter/material.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';
import '../services/weather_service.dart';
import '../models/weather_data.dart';
import '../widgets/weather_info_card.dart';

class WeatherMapScreen extends StatefulWidget {
  const WeatherMapScreen({super.key});

  @override
  State<WeatherMapScreen> createState() => _WeatherMapScreenState();
}

class _WeatherMapScreenState extends State<WeatherMapScreen> {
  final MapController _mapController = MapController();
  final WeatherService _weatherService = WeatherService();

  List<WeatherData> _weatherDataList = [];
  bool _isLoading = false;
  WeatherData? _selectedCityWeather;

  // Türkmenistanyň esasy şäherleri
  final List<Map<String, dynamic>> _turkmenCities = [
    {'name': 'Aşgabat', 'lat': 37.9601, 'lon': 58.3261},
    {'name': 'Türkmenabat', 'lat': 39.0739, 'lon': 63.5784},
    {'name': '<PERSON><PERSON><PERSON><PERSON>', 'lat': 41.8361, 'lon': 59.9667},
    {'name': 'Mary', 'lat': 37.5942, 'lon': 61.8306},
    {'name': 'Balkanabat', 'lat': 39.5108, 'lon': 54.3671},
    {'name': 'Türkmenbaşy', 'lat': 40.0225, 'lon': 52.9553},
    {'name': 'Serdar', 'lat': 38.9833, 'lon': 56.2667},
    {'name': 'Gözgala', 'lat': 37.9667, 'lon': 54.4167},
  ];

  @override
  void initState() {
    super.initState();
    _loadWeatherData();
  }

  Future<void> _loadWeatherData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Demo data for better presentation
      final demoWeatherData = [
        WeatherData(
          cityName: 'Aşgabat',
          temperature: 28.5,
          condition: 'clear',
          description: 'Açyk howa',
          icon: '01d',
          humidity: 45,
          windSpeed: 3.2,
          pressure: 1013,
          visibility: 10000,
          feelsLike: 31.2,
          latitude: 37.9601,
          longitude: 58.3261,
          dateTime: DateTime.now(),
        ),
        WeatherData(
          cityName: 'Türkmenabat',
          temperature: 32.1,
          condition: 'clouds',
          description: 'Bulutly',
          icon: '02d',
          humidity: 38,
          windSpeed: 2.8,
          pressure: 1011,
          visibility: 8500,
          feelsLike: 35.4,
          latitude: 39.0739,
          longitude: 63.5784,
          dateTime: DateTime.now(),
        ),
        WeatherData(
          cityName: 'Daşoguz',
          temperature: 26.8,
          condition: 'clear',
          description: 'Açyk howa',
          icon: '01d',
          humidity: 52,
          windSpeed: 4.1,
          pressure: 1015,
          visibility: 12000,
          feelsLike: 29.3,
          latitude: 41.8361,
          longitude: 59.9667,
          dateTime: DateTime.now(),
        ),
        WeatherData(
          cityName: 'Mary',
          temperature: 30.7,
          condition: 'clouds',
          description: 'Bulutly',
          icon: '02d',
          humidity: 41,
          windSpeed: 2.5,
          pressure: 1012,
          visibility: 9000,
          feelsLike: 33.8,
          latitude: 37.5942,
          longitude: 61.8306,
          dateTime: DateTime.now(),
        ),
        WeatherData(
          cityName: 'Balkanabat',
          temperature: 25.3,
          condition: 'clear',
          description: 'Açyk howa',
          icon: '01d',
          humidity: 58,
          windSpeed: 5.2,
          pressure: 1016,
          visibility: 11000,
          feelsLike: 27.9,
          latitude: 39.5108,
          longitude: 54.3671,
          dateTime: DateTime.now(),
        ),
        WeatherData(
          cityName: 'Türkmenbaşy',
          temperature: 24.1,
          condition: 'rain',
          description: 'Ýagyşly',
          icon: '10d',
          humidity: 72,
          windSpeed: 6.8,
          pressure: 1009,
          visibility: 6000,
          feelsLike: 26.5,
          latitude: 40.0225,
          longitude: 52.9553,
          dateTime: DateTime.now(),
        ),
        WeatherData(
          cityName: 'Serdar',
          temperature: 29.4,
          condition: 'clear',
          description: 'Açyk howa',
          icon: '01d',
          humidity: 43,
          windSpeed: 3.7,
          pressure: 1014,
          visibility: 10500,
          feelsLike: 32.1,
          latitude: 38.9833,
          longitude: 56.2667,
          dateTime: DateTime.now(),
        ),
        WeatherData(
          cityName: 'Gözgala',
          temperature: 27.6,
          condition: 'clouds',
          description: 'Bulutly',
          icon: '02d',
          humidity: 49,
          windSpeed: 4.3,
          pressure: 1013,
          visibility: 9500,
          feelsLike: 30.2,
          latitude: 37.9667,
          longitude: 54.4167,
          dateTime: DateTime.now(),
        ),
      ];

      setState(() {
        _weatherDataList = demoWeatherData;
        _isLoading = false;
      });

    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Howa maglumatlary ýüklenip bilmedi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        title: const Text(
          'Türkmenistan Howa Maglumaty',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: Colors.white,
            fontSize: 20,
          ),
        ),
        backgroundColor: const Color(0xFF00A651).withValues(alpha: 0.9),
        elevation: 0,
        centerTitle: true,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 8),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: IconButton(
              icon: const Icon(Icons.refresh, color: Colors.white, size: 24),
              onPressed: _loadWeatherData,
              tooltip: 'Täzele',
            ),
          ),
        ],
        leading: Container(
          margin: const EdgeInsets.only(left: 8),
          decoration: BoxDecoration(
            color: Colors.white.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
          ),
          child: IconButton(
            icon: const Icon(Icons.location_on, color: Colors.white, size: 24),
            onPressed: () {
              _mapController.move(const LatLng(38.9697, 59.5563), 6.0);
            },
            tooltip: 'Merkeze git',
          ),
        ),
      ),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF87CEEB), // Sky blue
              Color(0xFFE0F6FF), // Light blue
            ],
          ),
        ),
        child: _isLoading
            ? Center(
                child: Container(
                  padding: const EdgeInsets.all(32),
                  decoration: BoxDecoration(
                    color: Colors.white.withValues(alpha: 0.95),
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.1),
                        blurRadius: 20,
                        offset: const Offset(0, 10),
                      ),
                    ],
                  ),
                  child: const Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      CircularProgressIndicator(
                        valueColor: AlwaysStoppedAnimation<Color>(Color(0xFF00A651)),
                        strokeWidth: 3,
                      ),
                      SizedBox(height: 16),
                      Text(
                        'Howa maglumatlary ýüklenýär...',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF00A651),
                        ),
                      ),
                    ],
                  ),
                ),
              )
            : Stack(
                children: [
                  // Ana harita
                  Positioned.fill(
                    top: 100,
                    child: Container(
                      margin: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 20,
                            offset: const Offset(0, 10),
                          ),
                        ],
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(20),
                        child: FlutterMap(
                          mapController: _mapController,
                          options: MapOptions(
                            initialCenter: const LatLng(38.9697, 59.5563),
                            initialZoom: 6.0,
                            minZoom: 5.0,
                            maxZoom: 10.0,
                            onTap: (tapPosition, point) {
                              setState(() {
                                _selectedCityWeather = null;
                              });
                            },
                          ),
                          children: [
                            TileLayer(
                              urlTemplate: 'https://tile.openstreetmap.org/{z}/{x}/{y}.png',
                              userAgentPackageName: 'com.example.turkmen_weather',
                            ),
                            MarkerLayer(
                              markers: _weatherDataList.map((weather) {
                                return Marker(
                                  point: LatLng(weather.latitude, weather.longitude),
                                  width: 100,
                                  height: 100,
                                  child: GestureDetector(
                                    onTap: () {
                                      setState(() {
                                        _selectedCityWeather = weather;
                                      });
                                    },
                                    child: Container(
                                      decoration: BoxDecoration(
                                        gradient: const LinearGradient(
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                          colors: [
                                            Color(0xFF00A651),
                                            Color(0xFF00D463),
                                          ],
                                        ),
                                        borderRadius: BorderRadius.circular(50),
                                        border: Border.all(
                                          color: Colors.white,
                                          width: 3,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Colors.black.withValues(alpha: 0.3),
                                            blurRadius: 8,
                                            offset: const Offset(0, 4),
                                          ),
                                        ],
                                      ),
                                      child: Column(
                                        mainAxisAlignment: MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            _getWeatherIcon(weather.condition),
                                            size: 32,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            '${weather.temperature.round()}°',
                                            style: const TextStyle(
                                              fontSize: 14,
                                              fontWeight: FontWeight.bold,
                                              color: Colors.white,
                                            ),
                                          ),
                                          Text(
                                            weather.cityName,
                                            style: const TextStyle(
                                              fontSize: 10,
                                              fontWeight: FontWeight.w500,
                                              color: Colors.white,
                                            ),
                                            textAlign: TextAlign.center,
                                            maxLines: 1,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                );
                              }).toList(),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),

          // Saýlanan şäheriň howa durumu kartasy
          if (_selectedCityWeather != null)
            Positioned(
              bottom: 20,
              left: 20,
              right: 20,
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.2),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: WeatherInfoCard(weather: _selectedCityWeather!),
              ),
            ),

          // Kömek teksti
          if (_selectedCityWeather == null && !_isLoading)
            Positioned(
              bottom: 30,
              left: 20,
              right: 20,
              child: Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.95),
                  borderRadius: BorderRadius.circular(15),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 10,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: const Row(
                  children: [
                    Icon(
                      Icons.touch_app,
                      color: Color(0xFF00A651),
                      size: 24,
                    ),
                    SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        'Howa durumyny görmek üçin kartadaky şäherlere basyň',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: Color(0xFF333333),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  IconData _getWeatherIcon(String condition) {
    switch (condition.toLowerCase()) {
      case 'clear':
        return Icons.wb_sunny;
      case 'clouds':
        return Icons.cloud;
      case 'rain':
        return Icons.grain;
      case 'snow':
        return Icons.ac_unit;
      case 'thunderstorm':
        return Icons.flash_on;
      case 'drizzle':
        return Icons.grain;
      case 'mist':
      case 'fog':
        return Icons.cloud;
      default:
        return Icons.wb_sunny;
    }
  }
}
