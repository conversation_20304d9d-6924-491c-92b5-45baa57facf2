import '../models/weather_data.dart';

class DemoWeatherService {
  // Demo howa durumu maglumatlary
  static List<WeatherData> getDemoWeatherData() {
    return [
      WeatherData(
        cityName: 'Aşgabat',
        temperature: 28.5,
        feelsLike: 31.2,
        humidity: 45,
        windSpeed: 3.2,
        pressure: 1013,
        condition: 'Clear',
        description: 'açyk howa',
        icon: '01d',
        latitude: 37.9601,
        longitude: 58.3261,
        dateTime: DateTime.now(),
        visibility: 10000,
        tempMin: 22.0,
        tempMax: 32.0,
      ),
      WeatherData(
        cityName: 'Türkmenabat',
        temperature: 26.8,
        feelsLike: 29.1,
        humidity: 52,
        windSpeed: 2.8,
        pressure: 1015,
        condition: 'Clouds',
        description: 'az bulutly',
        icon: '02d',
        latitude: 39.0739,
        longitude: 63.5784,
        dateTime: DateTime.now(),
        visibility: 8500,
        tempMin: 20.0,
        tempMax: 30.0,
      ),
      WeatherData(
        cityName: 'Daşoguz',
        temperature: 24.3,
        feelsLike: 26.7,
        humidity: 58,
        windSpeed: 4.1,
        pressure: 1018,
        condition: 'Clouds',
        description: 'bulutly',
        icon: '03d',
        latitude: 41.8361,
        longitude: 59.9667,
        dateTime: DateTime.now(),
        visibility: 7200,
        tempMin: 18.0,
        tempMax: 28.0,
      ),
      WeatherData(
        cityName: 'Mary',
        temperature: 30.2,
        feelsLike: 33.8,
        humidity: 38,
        windSpeed: 2.1,
        pressure: 1011,
        condition: 'Clear',
        description: 'açyk howa',
        icon: '01d',
        latitude: 37.5942,
        longitude: 61.8306,
        dateTime: DateTime.now(),
        visibility: 10000,
        tempMin: 24.0,
        tempMax: 34.0,
      ),
      WeatherData(
        cityName: 'Balkanabat',
        temperature: 27.6,
        feelsLike: 30.4,
        humidity: 48,
        windSpeed: 5.3,
        pressure: 1014,
        condition: 'Clear',
        description: 'açyk howa',
        icon: '01d',
        latitude: 39.5108,
        longitude: 54.3671,
        dateTime: DateTime.now(),
        visibility: 9800,
        tempMin: 21.0,
        tempMax: 31.0,
      ),
      WeatherData(
        cityName: 'Türkmenbaşy',
        temperature: 25.9,
        feelsLike: 28.2,
        humidity: 62,
        windSpeed: 6.7,
        pressure: 1016,
        condition: 'Clouds',
        description: 'az bulutly',
        icon: '02d',
        latitude: 40.0225,
        longitude: 52.9553,
        dateTime: DateTime.now(),
        visibility: 8000,
        tempMin: 19.0,
        tempMax: 29.0,
      ),
      WeatherData(
        cityName: 'Serdar',
        temperature: 29.1,
        feelsLike: 32.5,
        humidity: 41,
        windSpeed: 3.8,
        pressure: 1012,
        condition: 'Clear',
        description: 'açyk howa',
        icon: '01d',
        latitude: 38.9833,
        longitude: 56.2667,
        dateTime: DateTime.now(),
        visibility: 10000,
        tempMin: 23.0,
        tempMax: 33.0,
      ),
      WeatherData(
        cityName: 'Gözgala',
        temperature: 28.7,
        feelsLike: 31.9,
        humidity: 44,
        windSpeed: 2.9,
        pressure: 1013,
        condition: 'Clear',
        description: 'açyk howa',
        icon: '01d',
        latitude: 37.9667,
        longitude: 54.4167,
        dateTime: DateTime.now(),
        visibility: 9500,
        tempMin: 22.0,
        tempMax: 32.0,
      ),
    ];
  }
}
